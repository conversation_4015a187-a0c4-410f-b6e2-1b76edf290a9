<?php

namespace App\Services;

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

class EmailService
{
    private PHPMailer $mailer;
    
    public function __construct()
    {
        $this->mailer = new PHPMailer(true);
        $this->configureMailer();
    }
    
    private function configureMailer(): void
    {
        try {
            // Server settings
            $this->mailer->isSMTP();
            $this->mailer->Host = $_ENV['SMTP_HOST'] ?? 'smtp.gmail.com';
            $this->mailer->SMTPAuth = true;
            $this->mailer->Username = $_ENV['SMTP_USERNAME'] ?? '';
            $this->mailer->Password = $_ENV['SMTP_PASSWORD'] ?? '';
            $this->mailer->SMTPSecure = $_ENV['SMTP_ENCRYPTION'] ?? PHPMailer::ENCRYPTION_STARTTLS;
            $this->mailer->Port = (int)($_ENV['SMTP_PORT'] ?? 587);
            
            // Default sender
            $this->mailer->setFrom(
                $_ENV['SMTP_FROM_EMAIL'] ?? '<EMAIL>',
                $_ENV['SMTP_FROM_NAME'] ?? 'Email Marketing Platform'
            );
            
            // Enable verbose debug output (disable in production)
            $this->mailer->SMTPDebug = SMTP::DEBUG_OFF;
            $this->mailer->Debugoutput = 'html';
            
        } catch (Exception $e) {
            throw new \Exception("Email configuration failed: " . $e->getMessage());
        }
    }
    
    public function sendEmail(string $to, string $subject, string $htmlBody, string $textBody = '', array $attachments = []): bool
    {
        try {
            // Clear any previous recipients
            $this->mailer->clearAddresses();
            $this->mailer->clearAttachments();
            
            // Recipients
            $this->mailer->addAddress($to);
            
            // Content
            $this->mailer->isHTML(true);
            $this->mailer->Subject = $subject;
            $this->mailer->Body = $htmlBody;
            $this->mailer->AltBody = $textBody ?: strip_tags($htmlBody);
            
            // Add attachments if any
            foreach ($attachments as $attachment) {
                if (isset($attachment['path']) && file_exists($attachment['path'])) {
                    $this->mailer->addAttachment(
                        $attachment['path'],
                        $attachment['name'] ?? basename($attachment['path'])
                    );
                }
            }
            
            return $this->mailer->send();
            
        } catch (Exception $e) {
            error_log("Email sending failed: " . $e->getMessage());
            return false;
        }
    }
    
    public function sendTestEmail(string $to, string $templateName, string $htmlContent): bool
    {
        $subject = "Test Email: {$templateName}";
        $htmlBody = $this->wrapInEmailTemplate($htmlContent, $templateName);
        
        return $this->sendEmail($to, $subject, $htmlBody);
    }
    
    public function sendCampaignEmail(string $to, string $subject, string $htmlContent, array $personalizations = []): bool
    {
        // Apply personalizations
        $personalizedContent = $this->applyPersonalizations($htmlContent, $personalizations);
        $personalizedSubject = $this->applyPersonalizations($subject, $personalizations);
        
        return $this->sendEmail($to, $personalizedSubject, $personalizedContent);
    }
    
    private function wrapInEmailTemplate(string $content, string $title = ''): string
    {
        return "
        <!DOCTYPE html>
        <html lang='en'>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>{$title}</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f4f4f4; }
                .email-container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
                .email-header { background-color: #2563eb; color: white; padding: 20px; text-align: center; }
                .email-content { padding: 20px; }
                .email-footer { background-color: #f8f9fa; padding: 15px; text-align: center; font-size: 12px; color: #666; }
            </style>
        </head>
        <body>
            <div class='email-container'>
                <div class='email-header'>
                    <h1>{$title}</h1>
                </div>
                <div class='email-content'>
                    {$content}
                </div>
                <div class='email-footer'>
                    <p>This is a test email from Email Marketing Platform</p>
                    <p>If you received this email in error, please ignore it.</p>
                </div>
            </div>
        </body>
        </html>";
    }
    
    private function applyPersonalizations(string $content, array $personalizations): string
    {
        foreach ($personalizations as $key => $value) {
            $content = str_replace("{{" . $key . "}}", $value, $content);
        }
        return $content;
    }
    
    public function validateEmailConfiguration(): array
    {
        $errors = [];
        
        if (empty($_ENV['SMTP_HOST'])) {
            $errors[] = 'SMTP_HOST is not configured';
        }
        
        if (empty($_ENV['SMTP_USERNAME'])) {
            $errors[] = 'SMTP_USERNAME is not configured';
        }
        
        if (empty($_ENV['SMTP_PASSWORD'])) {
            $errors[] = 'SMTP_PASSWORD is not configured';
        }
        
        if (empty($_ENV['SMTP_FROM_EMAIL'])) {
            $errors[] = 'SMTP_FROM_EMAIL is not configured';
        }
        
        return $errors;
    }
    
    public function testConnection(): bool
    {
        try {
            return $this->mailer->smtpConnect();
        } catch (Exception $e) {
            error_log("SMTP connection test failed: " . $e->getMessage());
            return false;
        }
    }
}
