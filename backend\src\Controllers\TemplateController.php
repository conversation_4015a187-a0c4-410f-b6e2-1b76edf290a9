<?php

namespace App\Controllers;

class TemplateController extends BaseController
{
    public function index(): void
    {
        $user = $this->requireAuth();
        
        try {
            $stmt = $this->db->prepare("
                SELECT * FROM templates
                WHERE user_id = ?
                ORDER BY updated_at DESC
            ");
            $stmt->execute([$user['id']]);
            $templates = $stmt->fetchAll();

            // Decode JSON fields for each template
            foreach ($templates as &$template) {
                if ($template['components']) {
                    $template['components'] = json_decode($template['components'], true);
                }
                if ($template['settings']) {
                    $template['settings'] = json_decode($template['settings'], true);
                }
            }

            $this->successResponse($templates);
        } catch (\Exception $e) {
            $this->errorResponse('Failed to fetch templates', 500);
        }
    }
    
    public function store(): void
    {
        $user = $this->requireAuth();
        $data = $this->getJsonInput();

        $errors = $this->validateRequired($data, ['name', 'html_content']);
        if (!empty($errors)) {
            $this->errorResponse('Validation failed', 422, $errors);
            return;
        }
        
        try {
            $stmt = $this->db->prepare("
                INSERT INTO templates (user_id, name, description, html_content, components, settings)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $user['id'],
                $data['name'],
                $data['description'] ?? null,
                $data['html_content'],
                isset($data['components']) ? json_encode($data['components']) : null,
                isset($data['settings']) ? json_encode($data['settings']) : null
            ]);
            
            $templateId = $this->db->lastInsertId();
            
            // Get the created template
            $stmt = $this->db->prepare("SELECT * FROM templates WHERE id = ?");
            $stmt->execute([$templateId]);
            $template = $stmt->fetch();

            // Decode JSON fields
            if ($template['components']) {
                $template['components'] = json_decode($template['components'], true);
            }
            if ($template['settings']) {
                $template['settings'] = json_decode($template['settings'], true);
            }

            $this->successResponse($template, 'Template created successfully');
        } catch (\Exception $e) {
            $this->errorResponse('Failed to create template', 500);
        }
    }
    
    public function show(string $id): void
    {
        $user = $this->requireAuth();
        
        try {
            $stmt = $this->db->prepare("
                SELECT * FROM templates 
                WHERE id = ? AND user_id = ?
            ");
            $stmt->execute([$id, $user['id']]);
            $template = $stmt->fetch();
            
            if (!$template) {
                $this->errorResponse('Template not found', 404);
                return;
            }

            // Decode JSON fields
            if ($template['components']) {
                $template['components'] = json_decode($template['components'], true);
            }
            if ($template['settings']) {
                $template['settings'] = json_decode($template['settings'], true);
            }

            $this->successResponse($template);
        } catch (\Exception $e) {
            $this->errorResponse('Failed to fetch template', 500);
        }
    }
    
    public function update(string $id): void
    {
        $user = $this->requireAuth();
        $data = $this->getJsonInput();
        
        try {
            // Check if template exists and belongs to user
            $stmt = $this->db->prepare("
                SELECT id FROM templates 
                WHERE id = ? AND user_id = ?
            ");
            $stmt->execute([$id, $user['id']]);
            
            if (!$stmt->fetch()) {
                $this->errorResponse('Template not found', 404);
                return;
            }
            
            // Build update query dynamically
            $updateFields = [];
            $updateValues = [];
            
            if (isset($data['name'])) {
                $updateFields[] = 'name = ?';
                $updateValues[] = $data['name'];
            }
            
            if (isset($data['description'])) {
                $updateFields[] = 'description = ?';
                $updateValues[] = $data['description'];
            }
            
            if (isset($data['html_content'])) {
                $updateFields[] = 'html_content = ?';
                $updateValues[] = $data['html_content'];
            }

            if (isset($data['components'])) {
                $updateFields[] = 'components = ?';
                $updateValues[] = json_encode($data['components']);
            }

            if (isset($data['settings'])) {
                $updateFields[] = 'settings = ?';
                $updateValues[] = json_encode($data['settings']);
            }

            if (empty($updateFields)) {
                $this->errorResponse('No fields to update', 400);
                return;
            }
            
            $updateValues[] = $id;
            
            $stmt = $this->db->prepare("
                UPDATE templates 
                SET " . implode(', ', $updateFields) . " 
                WHERE id = ?
            ");
            $stmt->execute($updateValues);
            
            // Get the updated template
            $stmt = $this->db->prepare("SELECT * FROM templates WHERE id = ?");
            $stmt->execute([$id]);
            $template = $stmt->fetch();

            // Decode JSON fields
            if ($template['components']) {
                $template['components'] = json_decode($template['components'], true);
            }
            if ($template['settings']) {
                $template['settings'] = json_decode($template['settings'], true);
            }

            $this->successResponse($template, 'Template updated successfully');
        } catch (\Exception $e) {
            $this->errorResponse('Failed to update template', 500);
        }
    }
    
    public function destroy(string $id): void
    {
        $user = $this->requireAuth();
        
        try {
            $stmt = $this->db->prepare("
                DELETE FROM templates 
                WHERE id = ? AND user_id = ?
            ");
            $stmt->execute([$id, $user['id']]);
            
            if ($stmt->rowCount() === 0) {
                $this->errorResponse('Template not found', 404);
                return;
            }
            
            $this->successResponse(null, 'Template deleted successfully');
        } catch (\Exception $e) {
            $this->errorResponse('Failed to delete template', 500);
        }
    }
}
