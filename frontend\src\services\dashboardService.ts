import { apiService } from './api'

export interface DashboardStats {
  totalTemplates: number
  totalContacts: number
  totalCampaigns: number
  emailsSent: number
  openRate: number
  clickRate: number
  recentActivity: ActivityItem[]
  campaignPerformance: CampaignPerformance[]
  contactGrowth: ContactGrowthData[]
}

export interface ActivityItem {
  id: string
  type: 'template_created' | 'campaign_sent' | 'contact_added' | 'email_opened' | 'email_clicked'
  title: string
  description: string
  timestamp: string
  icon: string
}

export interface CampaignPerformance {
  id: number
  name: string
  sent: number
  delivered: number
  opened: number
  clicked: number
  bounced: number
  openRate: number
  clickRate: number
  sentAt: string
}

export interface ContactGrowthData {
  date: string
  total: number
  active: number
  unsubscribed: number
}

export const dashboardService = {
  // Get dashboard statistics
  getDashboardStats: async (): Promise<DashboardStats> => {
    return apiService.get<DashboardStats>('/dashboard/stats')
  },

  // Get recent activity
  getRecentActivity: async (limit: number = 10): Promise<ActivityItem[]> => {
    return apiService.get<ActivityItem[]>(`/dashboard/activity?limit=${limit}`)
  },

  // Get campaign performance data
  getCampaignPerformance: async (limit: number = 5): Promise<CampaignPerformance[]> => {
    return apiService.get<CampaignPerformance[]>(`/dashboard/campaigns?limit=${limit}`)
  },

  // Get contact growth data
  getContactGrowth: async (days: number = 30): Promise<ContactGrowthData[]> => {
    return apiService.get<ContactGrowthData[]>(`/dashboard/contacts/growth?days=${days}`)
  }
}
